---
/**
 * Bouton de cycle de thème : light → dark → system.
 * - stocke le mode dans localStorage('theme'): 'light' | 'dark' | 'system'
 * - applique la classe html.theme-dark si besoin
 * - suit l'OS quand 'system'
 *
 * Props:
 *   label? : string  (texte sur le bouton)
 *   class? : string  (classes CSS additionnelles)
 */
const { label = "Thème", class: extra = "" } = Astro.props;
---

<script is:inline>
  document.addEventListener("DOMContentLoaded", () => {
    const rootEl = document.documentElement;
    // const host = document.currentScript.closest(".theme-mode-cycle");
    const btn = document.querySelector("[data-theme-cycle]");
    const text = document.querySelector("[data-mode-text]");
    const media = window.matchMedia("(prefers-color-scheme: dark)");
    const KEY = "theme";

    const getSaved = () => {
      const v = localStorage.getItem(KEY);
      return v === "light" || v === "dark" || v === "system" ? v : "system";
    };

    const setClassFor = (mode) => {
      if (mode === "dark") {
        rootEl.classList.add("theme-dark");
      } else if (mode === "light") {
        rootEl.classList.remove("theme-dark");
      } else {
        // system: suit l'OS
        if (media.matches) rootEl.classList.add("theme-dark");
        else rootEl.classList.remove("theme-dark");
      }
    };

    const setMode = (mode) => {
      localStorage.setItem(KEY, mode);
      setClassFor(mode);
      btn?.setAttribute("data-mode", mode);
      btn?.setAttribute("aria-label", `Basculer le thème (actuel: ${mode})`);
      if (text)
        text.textContent =
          mode === "light" ? "Clair" : mode === "dark" ? "Sombre" : "Système";
      // (optionnel) synchroniser d'autres boutons:
      document.dispatchEvent(
        new CustomEvent("theme:changed", { detail: { mode } })
      );
    };

    const next = (mode) =>
      mode === "light" ? "dark" : mode === "dark" ? "system" : "light";

    // init (sans FOUC excessif)
    const initial = getSaved();
    setClassFor(initial);
    setMode(initial);

    // clic = cycle
    btn?.addEventListener("click", () => setMode(next(getSaved())));

    // si l'OS change et qu'on est en 'system' → on suit
    media.addEventListener("change", () => {
      console.log("OS change", media.matches);
      if (getSaved() === "system") setMode("system");
    });
  });
</script>

<div class={`theme-mode-cycle ${extra}`}>
  <button type="button" class="cycle-btn" data-theme-cycle data-mode="system">
    <span class="cycle-label">{label}</span>
    <span class="cycle-dot" aria-hidden="true"></span>
    <span class="cycle-mode" data-mode-text>​</span>
  </button>
</div>

<style>
  .theme-mode-cycle {
    display: inline-flex;
  }

  .cycle-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-4);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background: var(--color-solid);
    color: var(--color-text-primary);
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition:
      background-color 0.2s ease,
      box-shadow 0.2s ease,
      transform 0.1s ease;
  }
  .cycle-btn:hover {
    background: color-mix(
      in srgb,
      var(--color-solid) 90%,
      var(--color-text-primary) 10%
    );
    box-shadow: 0 8px 24px rgb(0 0 0 / 0.08);
    transform: translateY(-1px);
  }
  .cycle-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgb(0 0 0 / 0.08);
  }
  .cycle-btn:focus-visible {
    outline: 2px solid var(--accent-regular);
    outline-offset: 3px;
  }

  .cycle-label {
    font-weight: 600;
    font-size: 0.9rem;
  }

  .cycle-mode {
    font-size: 0.85rem;
    color: var(--color-text-secondary);
  }

  /* petit indicateur: couleur selon mode */
  .cycle-dot {
    inline-size: 0.5rem;
    block-size: 0.5rem;
    border-radius: 999px;
    background: var(--accent-regular);
  }
  /* tu peux différencier visuellement par mode si tu veux :
     .cycle-btn[data-mode="light"]  .cycle-dot { background: #f4b400; }
     .cycle-btn[data-mode="dark"]   .cycle-dot { background: #6474a2; }
     .cycle-btn[data-mode="system"] .cycle-dot { background: #a3acc8; }
  */
</style>
